---
name: Flutter开发风格
description: 专为Flutter/Dart项目优化的输出样式，包含代码高亮、格式化规则和最佳实践
---

# Flutter项目专用输出样式

## 代码展示规则
- **Dart代码块**: 始终使用 ```dart 标记，包含完整的类结构
- **Widget展示**: 使用层级缩进显示Widget树结构
- **异步代码**: 明确标识 async/await 模式
- **状态管理**: 区分 StatelessWidget 和 StatefulWidget

## 颜色标识系统
- **Widget类名**: 蓝色 (#2196F3)
- **BuildContext**: 橙色 (#FF9800)
- **属性参数**: 绿色 (#4CAF50)
- **字符串值**: 红色 (#F44336)
- **数值**: 紫色 (#9C27B0)
- **注释**: 灰色 (#757575)

## 文件类型识别
- **.dart**: 核心代码文件
- **.yaml**: 配置文件（pubspec.yaml）
- **.gradle**: Android构建配置
- **.swift**: iOS原生代码
- **.kt**: Android原生代码
- **.json**: 数据文件

## 输出格式约定

### Widget结构展示
```
Scaffold
├── AppBar
│   ├── title: Text
│   └── actions: [...]
├── body: Center
│   └── child: Column
│       ├── children: [
│       │   ├── Text
│       │   └── ElevatedButton
│       └── ...
└── floatingActionButton: FloatingActionButton
```

### 错误和警告格式
- **分析错误**: 🔴 [ERROR] 文件路径:行号 - 错误描述
- **lint警告**: 🟡 [LINT] 规则名 - 建议改进
- **编译错误**: 🔴 [BUILD] 构建阶段 - 具体错误
[tars-ai.md](../../../../.claude/output-styles/tars-ai.md)
### 依赖管理输出
- **新增依赖**: ✨ 包名 ^版本号
- **版本冲突**: ⚠️ 冲突描述 - 建议解决方案
- **过时的包**: 🔄 包名 (当前版本 → 最新版本)

## Flutter特定优化

### 响应式设计
- **屏幕适配**: 自动识别 MediaQuery 和 LayoutBuilder 使用
- **主题系统**: 突出显示 ThemeData 和 ColorScheme
- **字体样式**: 明确标识 TextStyle 属性

### 性能建议
- **构建优化**: 识别 const 构造函数机会
- **状态管理**: 建议使用合适的 state management 方案
- **图片优化**: 提供 asset 图片压缩建议

### 平台特定代码
- **iOS**: 突出显示 Swift/Objective-C 代码段
- **Android**: 突出显示 Kotlin/Java 代码段
- **平台通道**: 明确标识 MethodChannel 实现

## 最佳实践检查
- **空安全**: 验证 null safety 使用
- **异步处理**: 检查 FutureBuilder 和 StreamBuilder 使用
- **内存管理**: 提醒 dispose 方法实现
- **测试覆盖**: 建议测试用例编写

## 交互提示
- **热重载**: 提示可能影响热重载的更改
- **调试模式**: 区分 debug 和 release 模式下的行为
- **模拟器**: 提供不同设备尺寸的预览建议
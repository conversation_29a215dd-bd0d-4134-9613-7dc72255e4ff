import 'package:flutter/material.dart';
import '../ui/design_spec.dart';

class StoryCategoryChip extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final bool selected;
  final VoidCallback onTap;

  const StoryCategoryChip({
    super.key,
    required this.icon,
    required this.label,
    required this.color,
    required this.selected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final bg = selected ? color : color.withOpacity(0.6);
    return InkWell(
      borderRadius: BorderRadius.circular(18),
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: bg,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Icon(icon, color: Colors.white),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: bg.withOpacity(0.85),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(14),
                bottom: Radius.circular(20),
              ),
            ),
            child: Text(
              label,
              style: TextStyle(
                color: Colors.white,
                fontWeight: DesignSpec.fontWeightSemiBold,
                fontSize: DesignSpec.fontSizeXs,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

